{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Stock_Market\\\\frontend\\\\src\\\\App.js\";\nimport { BrowserRouter as Router, Routes, Route } from \"react-router-dom\";\nimport Login from \"./pages/Login\";\nimport Signup from \"./pages/Signup\";\nimport Navbar from \"./components/Navbar\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/signup\",\n        element: /*#__PURE__*/_jsxDEV(Signup, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 40\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON><PERSON>", "Signup", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Stock_Market/frontend/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Routes, Route } from \"react-router-dom\";\nimport Login from \"./pages/Login\";\nimport Signup from \"./pages/Signup\";\nimport Navbar from \"./components/Navbar\";\n\nfunction App() {\n  return (\n    <Router>\n      <Navbar />\n      <Routes>\n        <Route path=\"/\" element={<Login />} />\n        <Route path=\"/signup\" element={<Signup />} />\n      </Routes>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACP,MAAM;IAAAS,QAAA,gBACLF,OAAA,CAACF,MAAM;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVN,OAAA,CAACN,MAAM;MAAAQ,QAAA,gBACLF,OAAA,CAACL,KAAK;QAACY,IAAI,EAAC,GAAG;QAACC,OAAO,eAAER,OAAA,CAACJ,KAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtCN,OAAA,CAACL,KAAK;QAACY,IAAI,EAAC,SAAS;QAACC,OAAO,eAAER,OAAA,CAACH,MAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACG,EAAA,GAVQR,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import createMachine from \"./fsm.js\";\n\n/**\n * @typedef {Object} ShowOverlayData\n * @property {'warning' | 'error'} level\n * @property {Array<string  | { moduleIdentifier?: string, moduleName?: string, loc?: string, message?: string }>} messages\n * @property {'build' | 'runtime'} messageSource\n */\n\n/**\n * @typedef {Object} CreateOverlayMachineOptions\n * @property {(data: ShowOverlayData) => void} showOverlay\n * @property {() => void} hideOverlay\n */\n\n/**\n * @param {CreateOverlayMachineOptions} options\n */\nvar createOverlayMachine = function createOverlayMachine(options) {\n  var hideOverlay = options.hideOverlay,\n    showOverlay = options.showOverlay;\n  var overlayMachine = createMachine({\n    initial: \"hidden\",\n    context: {\n      level: \"error\",\n      messages: [],\n      messageSource: \"build\"\n    },\n    states: {\n      hidden: {\n        on: {\n          BUILD_ERROR: {\n            target: \"displayBuildError\",\n            actions: [\"setMessages\", \"showOverlay\"]\n          },\n          RUNTIME_ERROR: {\n            target: \"displayRuntimeError\",\n            actions: [\"setMessages\", \"showOverlay\"]\n          }\n        }\n      },\n      displayBuildError: {\n        on: {\n          DISMISS: {\n            target: \"hidden\",\n            actions: [\"dismissMessages\", \"hideOverlay\"]\n          },\n          BUILD_ERROR: {\n            target: \"displayBuildError\",\n            actions: [\"appendMessages\", \"showOverlay\"]\n          }\n        }\n      },\n      displayRuntimeError: {\n        on: {\n          DISMISS: {\n            target: \"hidden\",\n            actions: [\"dismissMessages\", \"hideOverlay\"]\n          },\n          RUNTIME_ERROR: {\n            target: \"displayRuntimeError\",\n            actions: [\"appendMessages\", \"showOverlay\"]\n          },\n          BUILD_ERROR: {\n            target: \"displayBuildError\",\n            actions: [\"setMessages\", \"showOverlay\"]\n          }\n        }\n      }\n    }\n  }, {\n    actions: {\n      dismissMessages: function dismissMessages() {\n        return {\n          messages: [],\n          level: \"error\",\n          messageSource: \"build\"\n        };\n      },\n      appendMessages: function appendMessages(context, event) {\n        return {\n          messages: context.messages.concat(event.messages),\n          level: event.level || context.level,\n          messageSource: event.type === \"RUNTIME_ERROR\" ? \"runtime\" : \"build\"\n        };\n      },\n      setMessages: function setMessages(context, event) {\n        return {\n          messages: event.messages,\n          level: event.level || context.level,\n          messageSource: event.type === \"RUNTIME_ERROR\" ? \"runtime\" : \"build\"\n        };\n      },\n      hideOverlay: hideOverlay,\n      showOverlay: showOverlay\n    }\n  });\n  return overlayMachine;\n};\nexport default createOverlayMachine;", "map": {"version": 3, "names": ["createMachine", "createOverlayMachine", "options", "hideOverlay", "showOverlay", "overlayMachine", "initial", "context", "level", "messages", "messageSource", "states", "hidden", "on", "BUILD_ERROR", "target", "actions", "RUNTIME_ERROR", "displayBuildError", "DISMISS", "displayRuntimeError", "dismissMessages", "appendMessages", "event", "concat", "type", "setMessages"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Stock_Market/frontend/node_modules/webpack-dev-server/client/overlay/state-machine.js"], "sourcesContent": ["import createMachine from \"./fsm.js\";\n\n/**\n * @typedef {Object} ShowOverlayData\n * @property {'warning' | 'error'} level\n * @property {Array<string  | { moduleIdentifier?: string, moduleName?: string, loc?: string, message?: string }>} messages\n * @property {'build' | 'runtime'} messageSource\n */\n\n/**\n * @typedef {Object} CreateOverlayMachineOptions\n * @property {(data: ShowOverlayData) => void} showOverlay\n * @property {() => void} hideOverlay\n */\n\n/**\n * @param {CreateOverlayMachineOptions} options\n */\nvar createOverlayMachine = function createOverlayMachine(options) {\n  var hideOverlay = options.hideOverlay,\n    showOverlay = options.showOverlay;\n  var overlayMachine = createMachine({\n    initial: \"hidden\",\n    context: {\n      level: \"error\",\n      messages: [],\n      messageSource: \"build\"\n    },\n    states: {\n      hidden: {\n        on: {\n          BUILD_ERROR: {\n            target: \"displayBuildError\",\n            actions: [\"setMessages\", \"showOverlay\"]\n          },\n          RUNTIME_ERROR: {\n            target: \"displayRuntimeError\",\n            actions: [\"setMessages\", \"showOverlay\"]\n          }\n        }\n      },\n      displayBuildError: {\n        on: {\n          DISMISS: {\n            target: \"hidden\",\n            actions: [\"dismissMessages\", \"hideOverlay\"]\n          },\n          BUILD_ERROR: {\n            target: \"displayBuildError\",\n            actions: [\"appendMessages\", \"showOverlay\"]\n          }\n        }\n      },\n      displayRuntimeError: {\n        on: {\n          DISMISS: {\n            target: \"hidden\",\n            actions: [\"dismissMessages\", \"hideOverlay\"]\n          },\n          RUNTIME_ERROR: {\n            target: \"displayRuntimeError\",\n            actions: [\"appendMessages\", \"showOverlay\"]\n          },\n          BUILD_ERROR: {\n            target: \"displayBuildError\",\n            actions: [\"setMessages\", \"showOverlay\"]\n          }\n        }\n      }\n    }\n  }, {\n    actions: {\n      dismissMessages: function dismissMessages() {\n        return {\n          messages: [],\n          level: \"error\",\n          messageSource: \"build\"\n        };\n      },\n      appendMessages: function appendMessages(context, event) {\n        return {\n          messages: context.messages.concat(event.messages),\n          level: event.level || context.level,\n          messageSource: event.type === \"RUNTIME_ERROR\" ? \"runtime\" : \"build\"\n        };\n      },\n      setMessages: function setMessages(context, event) {\n        return {\n          messages: event.messages,\n          level: event.level || context.level,\n          messageSource: event.type === \"RUNTIME_ERROR\" ? \"runtime\" : \"build\"\n        };\n      },\n      hideOverlay: hideOverlay,\n      showOverlay: showOverlay\n    }\n  });\n  return overlayMachine;\n};\nexport default createOverlayMachine;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,UAAU;;AAEpC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,OAAO,EAAE;EAChE,IAAIC,WAAW,GAAGD,OAAO,CAACC,WAAW;IACnCC,WAAW,GAAGF,OAAO,CAACE,WAAW;EACnC,IAAIC,cAAc,GAAGL,aAAa,CAAC;IACjCM,OAAO,EAAE,QAAQ;IACjBC,OAAO,EAAE;MACPC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE;IACjB,CAAC;IACDC,MAAM,EAAE;MACNC,MAAM,EAAE;QACNC,EAAE,EAAE;UACFC,WAAW,EAAE;YACXC,MAAM,EAAE,mBAAmB;YAC3BC,OAAO,EAAE,CAAC,aAAa,EAAE,aAAa;UACxC,CAAC;UACDC,aAAa,EAAE;YACbF,MAAM,EAAE,qBAAqB;YAC7BC,OAAO,EAAE,CAAC,aAAa,EAAE,aAAa;UACxC;QACF;MACF,CAAC;MACDE,iBAAiB,EAAE;QACjBL,EAAE,EAAE;UACFM,OAAO,EAAE;YACPJ,MAAM,EAAE,QAAQ;YAChBC,OAAO,EAAE,CAAC,iBAAiB,EAAE,aAAa;UAC5C,CAAC;UACDF,WAAW,EAAE;YACXC,MAAM,EAAE,mBAAmB;YAC3BC,OAAO,EAAE,CAAC,gBAAgB,EAAE,aAAa;UAC3C;QACF;MACF,CAAC;MACDI,mBAAmB,EAAE;QACnBP,EAAE,EAAE;UACFM,OAAO,EAAE;YACPJ,MAAM,EAAE,QAAQ;YAChBC,OAAO,EAAE,CAAC,iBAAiB,EAAE,aAAa;UAC5C,CAAC;UACDC,aAAa,EAAE;YACbF,MAAM,EAAE,qBAAqB;YAC7BC,OAAO,EAAE,CAAC,gBAAgB,EAAE,aAAa;UAC3C,CAAC;UACDF,WAAW,EAAE;YACXC,MAAM,EAAE,mBAAmB;YAC3BC,OAAO,EAAE,CAAC,aAAa,EAAE,aAAa;UACxC;QACF;MACF;IACF;EACF,CAAC,EAAE;IACDA,OAAO,EAAE;MACPK,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;QAC1C,OAAO;UACLZ,QAAQ,EAAE,EAAE;UACZD,KAAK,EAAE,OAAO;UACdE,aAAa,EAAE;QACjB,CAAC;MACH,CAAC;MACDY,cAAc,EAAE,SAASA,cAAcA,CAACf,OAAO,EAAEgB,KAAK,EAAE;QACtD,OAAO;UACLd,QAAQ,EAAEF,OAAO,CAACE,QAAQ,CAACe,MAAM,CAACD,KAAK,CAACd,QAAQ,CAAC;UACjDD,KAAK,EAAEe,KAAK,CAACf,KAAK,IAAID,OAAO,CAACC,KAAK;UACnCE,aAAa,EAAEa,KAAK,CAACE,IAAI,KAAK,eAAe,GAAG,SAAS,GAAG;QAC9D,CAAC;MACH,CAAC;MACDC,WAAW,EAAE,SAASA,WAAWA,CAACnB,OAAO,EAAEgB,KAAK,EAAE;QAChD,OAAO;UACLd,QAAQ,EAAEc,KAAK,CAACd,QAAQ;UACxBD,KAAK,EAAEe,KAAK,CAACf,KAAK,IAAID,OAAO,CAACC,KAAK;UACnCE,aAAa,EAAEa,KAAK,CAACE,IAAI,KAAK,eAAe,GAAG,SAAS,GAAG;QAC9D,CAAC;MACH,CAAC;MACDtB,WAAW,EAAEA,WAAW;MACxBC,WAAW,EAAEA;IACf;EACF,CAAC,CAAC;EACF,OAAOC,cAAc;AACvB,CAAC;AACD,eAAeJ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Stock_Market\\\\frontend\\\\src\\\\components\\\\Navbar.js\";\nimport { Link } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Navbar() {\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-gray-900 text-white px-6 py-4 flex justify-between items-center shadow-md\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-xl font-bold\",\n      children: \"\\uD83D\\uDCC8 Stock Prediction\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-x-4\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"hover:text-blue-400\",\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/signup\",\n        className: \"hover:text-blue-400\",\n        children: \"Signup\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["Link", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Stock_Market/frontend/src/components/Navbar.js"], "sourcesContent": ["import { Link } from \"react-router-dom\";\n\nfunction Navbar() {\n  return (\n    <nav className=\"bg-gray-900 text-white px-6 py-4 flex justify-between items-center shadow-md\">\n      <h1 className=\"text-xl font-bold\">📈 Stock Prediction</h1>\n      <div className=\"space-x-4\">\n        <Link to=\"/\" className=\"hover:text-blue-400\">Login</Link>\n        <Link to=\"/signup\" className=\"hover:text-blue-400\">Signup</Link>\n      </div>\n    </nav>\n  );\n}\n\nexport default Navbar;\n"], "mappings": ";AAAA,SAASA,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,MAAMA,CAAA,EAAG;EAChB,oBACED,OAAA;IAAKE,SAAS,EAAC,8EAA8E;IAAAC,QAAA,gBAC3FH,OAAA;MAAIE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAC;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC1DP,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBH,OAAA,CAACF,IAAI;QAACU,EAAE,EAAC,GAAG;QAACN,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzDP,OAAA,CAACF,IAAI;QAACU,EAAE,EAAC,SAAS;QAACN,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACE,EAAA,GAVQR,MAAM;AAYf,eAAeA,MAAM;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}